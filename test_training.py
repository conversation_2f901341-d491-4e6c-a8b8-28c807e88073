import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
import joblib
import os

print("🚀 Simple Trading Bot Training Test")
print("=" * 50)

# Load one strategy file to test
print("📊 Loading Strategy 1 Data...")
df = pd.read_csv('output_signals_S1.csv')
print(f"✅ Loaded data with shape: {df.shape}")

# Check for missing values
print("🔍 Checking for missing values...")
nan_counts = df.isnull().sum()
print(f"✅ Columns with NaN values: {nan_counts[nan_counts > 0].to_dict()}")

# Fill NaN values in strategy signal column
df['strategy1_signal'] = df['strategy1_signal'].fillna(0)
print("✅ Filled NaN values in strategy1_signal")

# Create labels based on strategy signals
def create_labels(signal):
    if signal == 1:
        return 'BUY'
    elif signal == -1:
        return 'SELL'
    else:
        return 'HOLD'

df['strategy_label'] = df['strategy1_signal'].apply(create_labels)
print("✅ Strategy labels created")
print(f"✅ Label distribution: {df['strategy_label'].value_counts().to_dict()}")

# Filter for actionable signals
actionable_data = df[df['strategy_label'].isin(['BUY', 'SELL'])].copy()
print(f"✅ Actionable signals: {len(actionable_data)} rows")

if len(actionable_data) == 0:
    print("❌ No actionable signals found!")
    exit()

# Prepare features
exclude_columns = ['time', 'direction', 'strategy_label', 'strategy1_signal']
feature_columns = [col for col in actionable_data.columns if col not in exclude_columns]
print(f"✅ Selected {len(feature_columns)} features")

# Prepare data
X = actionable_data[feature_columns].copy()
y = actionable_data['strategy_label'].copy()

# Handle any remaining NaN values
X = X.fillna(X.mean())
print(f"✅ Feature matrix shape: {X.shape}")

# Encode labels
label_encoder = LabelEncoder()
y_encoded = label_encoder.fit_transform(y)
print(f"✅ Label encoding: {dict(zip(label_encoder.classes_, label_encoder.transform(label_encoder.classes_)))}")

# Train/Test Split
X_train, X_test, y_train, y_test = train_test_split(
    X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

print(f"✅ Training set: {X_train.shape[0]} samples")
print(f"✅ Test set: {X_test.shape[0]} samples")

# Train model
print("\n🤖 Training RandomForest Model...")
model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
model.fit(X_train, y_train)

# Evaluate
y_pred = model.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)

print(f"✅ Accuracy: {accuracy:.4f}")
print("\n📋 Classification Report:")
print(classification_report(y_test, y_pred, target_names=label_encoder.classes_))

# Save model
joblib.dump(model, 'simple_trained_model.pkl')
joblib.dump(label_encoder, 'simple_label_encoder.pkl')
joblib.dump(feature_columns, 'simple_feature_columns.pkl')

print("\n✅ Model saved successfully!")
print("🎉 Training completed!")
