#!/usr/bin/env python3
"""
Trading Bot Launcher
Main entry point for the trading bot system
"""

import sys
import os
from utils import print_colored, print_header

def show_menu():
    """Display the main menu"""
    print_header("🤖 TRADING BOT SYSTEM")
    print_colored("Choose an option:", "INFO", bold=True)
    print()
    print_colored("1. 🚀 Start Live Trading Bot", "BUY", bold=True)
    print_colored("2. 🔬 Run Backtesting System", "INFO", bold=True)
    print_colored("3. 🔗 Test API Connection", "INFO", bold=True)
    print_colored("4. 📊 View Current Directory", "INFO", bold=True)
    print_colored("5. ❌ Exit", "ERROR", bold=True)
    print()

def test_api_connection():
    """Test the Oanda API connection"""
    print_header("🔗 TESTING API CONNECTION")
    try:
        import test_oanda_connection
        # The test will run automatically when imported
    except Exception as e:
        print_colored(f"❌ Error testing API: {str(e)}", "ERROR")

def start_live_trading():
    """Start the live trading bot"""
    print_header("🚀 STARTING LIVE TRADING BOT")
    print_colored("⚠️  This will start live market monitoring", "WARNING", bold=True)
    print_colored("Press Ctrl+C to stop the bot at any time", "INFO")
    print()
    
    confirm = input("Start live trading? (y/n): ").strip().lower()
    if confirm == 'y':
        try:
            from live_trading_bot import main
            main()
        except Exception as e:
            print_colored(f"❌ Error starting live trading: {str(e)}", "ERROR")
    else:
        print_colored("❌ Live trading cancelled", "WARNING")

def start_backtesting():
    """Start the backtesting system"""
    print_header("🔬 STARTING BACKTESTING SYSTEM")
    try:
        from backtest_system import main
        main()
    except Exception as e:
        print_colored(f"❌ Error starting backtesting: {str(e)}", "ERROR")

def view_directory():
    """View current directory contents"""
    print_header("📊 CURRENT DIRECTORY CONTENTS")
    
    try:
        files = os.listdir('.')
        
        # Separate files by type
        python_files = [f for f in files if f.endswith('.py')]
        csv_files = [f for f in files if f.endswith('.csv')]
        pkl_files = [f for f in files if f.endswith('.pkl')]
        directories = [f for f in files if os.path.isdir(f)]
        other_files = [f for f in files if f not in python_files + csv_files + pkl_files + directories]
        
        if python_files:
            print_colored("🐍 Python Files:", "INFO", bold=True)
            for f in sorted(python_files):
                print_colored(f"   {f}", "INFO")
            print()
        
        if csv_files:
            print_colored("📊 CSV Data Files:", "SUCCESS", bold=True)
            for f in sorted(csv_files):
                print_colored(f"   {f}", "SUCCESS")
            print()
        
        if pkl_files:
            print_colored("🤖 Model Files:", "BUY", bold=True)
            for f in sorted(pkl_files):
                print_colored(f"   {f}", "BUY")
            print()
        
        if directories:
            print_colored("📁 Directories:", "HEADER", bold=True)
            for d in sorted(directories):
                print_colored(f"   {d}/", "HEADER")
            print()
        
        if other_files:
            print_colored("📄 Other Files:", "WARNING", bold=True)
            for f in sorted(other_files):
                print_colored(f"   {f}", "WARNING")
            print()
        
        print_colored(f"Total files: {len(files)}", "INFO", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Error viewing directory: {str(e)}", "ERROR")

def main():
    """Main launcher function"""
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-5): ").strip()
            
            if choice == '1':
                start_live_trading()
            elif choice == '2':
                start_backtesting()
            elif choice == '3':
                test_api_connection()
            elif choice == '4':
                view_directory()
            elif choice == '5':
                print_colored("👋 Goodbye!", "INFO", bold=True)
                break
            else:
                print_colored("❌ Invalid choice. Please enter 1-5.", "ERROR")
            
            # Wait for user to continue
            if choice in ['1', '2', '3', '4']:
                print()
                input("Press Enter to continue...")
                print()
            
        except KeyboardInterrupt:
            print_colored("\n👋 Goodbye!", "INFO", bold=True)
            break
        except Exception as e:
            print_colored(f"❌ Unexpected error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
