import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
import joblib
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

print("🚀 Enhanced Trading Bot Training System")
print("=" * 60)

# Configuration
STRATEGIES = ['S1', 'S3', 'S4']
STRATEGY_NAMES = {
    'S1': 'Breakout with Volume',
    'S3': 'Support/Resistance Rejection',
    'S4': 'Trendline Break with Rejection'
}

# File list
files = ['output_signals_S1.csv', 'output_signals_S3.csv', 'output_signals_S4.csv']
strategy_data = {}

print("\n📊 Loading Strategy Data...")
print("-" * 40)

# Load each strategy's data separately
for i, file in enumerate(files):
    strategy = STRATEGIES[i]
    path = os.path.join(os.getcwd(), file)
    df = pd.read_csv(path)
    print(f"✅ Loaded {file} with shape {df.shape}")

    # Add strategy identifier
    df['strategy_source'] = strategy
    strategy_data[strategy] = df

# Combine all data
print("\n🔄 Combining Strategy Data...")
data = pd.concat(strategy_data.values(), ignore_index=True)
print(f"✅ Total combined rows: {len(data)}")

# Check columns
print(f"✅ Columns in dataset: {len(data.columns)} features")
print("📋 Available columns:", list(data.columns))

# Data preprocessing and feature engineering
print("\n🛠️ Data Preprocessing & Feature Engineering...")
print("-" * 50)

# Check for missing values before dropping
print("🔍 Checking for missing values...")
nan_counts = data.isnull().sum()
print(f"✅ Columns with NaN values: {nan_counts[nan_counts > 0].to_dict()}")

# Fill NaN values in strategy signal columns with 0 (no signal)
strategy_signal_cols = ['strategy1_signal', 'strategy3_signal', 'strategy4_signal']
for col in strategy_signal_cols:
    if col in data.columns:
        data[col] = data[col].fillna(0)
        print(f"✅ Filled NaN values in {col} with 0")

# Create comprehensive target labels based on strategy signals
def create_strategy_labels(row):
    """Create labels based on strategy-specific signals"""
    strategy = row['strategy_source']
    signal_col = f'strategy{strategy[1:]}_signal'  # S1 -> strategy1_signal

    if signal_col in row.index:
        signal = row[signal_col]
        if signal == 1:
            return 'BUY'
        elif signal == -1:
            return 'SELL'
        else:
            return 'HOLD'
    else:
        # Fallback to direction column
        return row.get('direction', 'HOLD')

# Apply strategy-based labeling
print("🏷️ Creating strategy-based labels...")
data['strategy_label'] = data.apply(create_strategy_labels, axis=1)
print("✅ Strategy-based labels created")
print(f"✅ Label distribution: {data['strategy_label'].value_counts().to_dict()}")

# Drop rows with missing values in other columns (not strategy signals)
initial_rows = len(data)
# Only drop rows where essential features are missing
essential_cols = ['open', 'high', 'low', 'close', 'volume', 'rsi', 'macd']
data = data.dropna(subset=essential_cols)
print(f"✅ Rows after dropna(): {len(data)} (removed {initial_rows - len(data)} rows)")

# Filter for actionable signals (BUY/SELL only)
actionable_data = data[data['strategy_label'].isin(['BUY', 'SELL'])].copy()
print(f"✅ Actionable signals: {len(actionable_data)} rows")

if len(actionable_data) == 0:
    raise ValueError("❌ No actionable signals found. Check strategy signal generation.")

# Encode labels
label_encoder = LabelEncoder()
actionable_data['target'] = label_encoder.fit_transform(actionable_data['strategy_label'])
print(f"✅ Label encoding: {dict(zip(label_encoder.classes_, label_encoder.transform(label_encoder.classes_)))}")

# Feature Engineering
print("\n⚙️ Advanced Feature Engineering...")
print("-" * 40)

# Create additional technical features
def add_technical_features(df):
    """Add advanced technical analysis features"""
    df = df.copy()

    # Price-based features
    df['body_size'] = abs(df['close'] - df['open'])
    df['upper_wick'] = df['high'] - df[['open', 'close']].max(axis=1)
    df['lower_wick'] = df[['open', 'close']].min(axis=1) - df['low']
    df['total_range'] = df['high'] - df['low']

    # Wick ratios
    df['upper_wick_ratio'] = df['upper_wick'] / (df['body_size'] + 1e-8)
    df['lower_wick_ratio'] = df['lower_wick'] / (df['body_size'] + 1e-8)
    df['body_to_range_ratio'] = df['body_size'] / (df['total_range'] + 1e-8)

    # Volume features
    df['volume_price_trend'] = df['volume'] * (df['close'] - df['open'])
    df['volume_weighted_price'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3)

    # Momentum features
    df['price_momentum_3'] = df['close'].pct_change(3)
    df['price_momentum_5'] = df['close'].pct_change(5)
    df['volume_momentum_3'] = df['volume'].pct_change(3)

    # Volatility features
    df['price_volatility_5'] = df['close'].rolling(5).std()
    df['price_volatility_10'] = df['close'].rolling(10).std()

    # Strategy-specific features
    df['is_bullish_candle'] = (df['close'] > df['open']).astype(int)
    df['is_bearish_candle'] = (df['close'] < df['open']).astype(int)
    df['is_doji'] = (abs(df['close'] - df['open']) < 0.0001).astype(int)

    return df

# Apply feature engineering
actionable_data = add_technical_features(actionable_data)
print("✅ Technical features added")

# Remove infinite and NaN values created by feature engineering
actionable_data = actionable_data.replace([np.inf, -np.inf], np.nan)
actionable_data = actionable_data.dropna()
print(f"✅ Data after feature engineering: {len(actionable_data)} rows")

# Prepare features for training
print("\n🎯 Preparing Features for Training...")
print("-" * 45)

# Define feature columns to exclude
exclude_columns = [
    'time', 'direction', 'strategy_label', 'target', 'strategy_source',
    'strategy1_signal', 'strategy3_signal', 'strategy4_signal'
]

# Get feature columns
feature_columns = [col for col in actionable_data.columns if col not in exclude_columns]
print(f"✅ Selected {len(feature_columns)} features for training")

# Prepare feature matrix and target
X = actionable_data[feature_columns].copy()
y = actionable_data['target'].copy()

print(f"✅ Feature matrix shape: {X.shape}")
print(f"✅ Target distribution: {np.bincount(y)}")

# Feature scaling
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
X_scaled = pd.DataFrame(X_scaled, columns=feature_columns, index=X.index)

print("✅ Features scaled using StandardScaler")

# Train/Test Split with stratification
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y, test_size=0.2, random_state=42, stratify=y, shuffle=True
)

print(f"✅ Training set: {X_train.shape[0]} samples")
print(f"✅ Test set: {X_test.shape[0]} samples")

# Model Training and Evaluation
print("\n🤖 Advanced Model Training...")
print("-" * 40)

# Define multiple models for ensemble
models = {
    'RandomForest': RandomForestClassifier(
        n_estimators=200,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    ),
    'GradientBoosting': GradientBoostingClassifier(
        n_estimators=150,
        learning_rate=0.1,
        max_depth=8,
        random_state=42
    ),
    'LogisticRegression': LogisticRegression(
        random_state=42,
        max_iter=1000,
        C=1.0
    )
}

# Train individual models
trained_models = {}
model_scores = {}

print("Training individual models...")
for name, model in models.items():
    print(f"\n🔄 Training {name}...")

    # Train model
    model.fit(X_train, y_train)

    # Predictions
    y_pred = model.predict(X_test)
    y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None

    # Evaluate
    accuracy = accuracy_score(y_test, y_pred)

    # Cross-validation score
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')

    print(f"✅ {name} - Accuracy: {accuracy:.4f}")
    print(f"✅ {name} - CV Score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

    # Store results
    trained_models[name] = model
    model_scores[name] = {
        'accuracy': accuracy,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'predictions': y_pred,
        'probabilities': y_pred_proba
    }

# Create ensemble model
print("\n🎭 Creating Ensemble Model...")
ensemble_models = [(name, model) for name, model in trained_models.items()]
ensemble = VotingClassifier(estimators=ensemble_models, voting='soft')
ensemble.fit(X_train, y_train)

# Evaluate ensemble
ensemble_pred = ensemble.predict(X_test)
ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
ensemble_cv_scores = cross_val_score(ensemble, X_train, y_train, cv=5, scoring='accuracy')

print(f"✅ Ensemble - Accuracy: {ensemble_accuracy:.4f}")
print(f"✅ Ensemble - CV Score: {ensemble_cv_scores.mean():.4f} (+/- {ensemble_cv_scores.std() * 2:.4f})")

# Store ensemble results
trained_models['Ensemble'] = ensemble
model_scores['Ensemble'] = {
    'accuracy': ensemble_accuracy,
    'cv_mean': ensemble_cv_scores.mean(),
    'cv_std': ensemble_cv_scores.std(),
    'predictions': ensemble_pred,
    'probabilities': ensemble.predict_proba(X_test)[:, 1]
}

# Detailed Model Evaluation
print("\n📊 Detailed Model Evaluation...")
print("-" * 45)

# Find best model
best_model_name = max(model_scores.keys(), key=lambda x: model_scores[x]['accuracy'])
best_model = trained_models[best_model_name]
best_accuracy = model_scores[best_model_name]['accuracy']

print(f"🏆 Best Model: {best_model_name} (Accuracy: {best_accuracy:.4f})")

# Detailed classification report for best model
print(f"\n📋 Detailed Classification Report - {best_model_name}:")
print("-" * 50)
best_predictions = model_scores[best_model_name]['predictions']
print(classification_report(y_test, best_predictions,
                          target_names=label_encoder.classes_))

# Confusion Matrix
print(f"\n🔍 Confusion Matrix - {best_model_name}:")
cm = confusion_matrix(y_test, best_predictions)
print(cm)

# Feature Importance Analysis
print("\n🎯 Feature Importance Analysis...")
print("-" * 40)

if hasattr(best_model, 'feature_importances_'):
    # Get feature importances
    importances = best_model.feature_importances_
    feature_importance_df = pd.DataFrame({
        'feature': feature_columns,
        'importance': importances
    }).sort_values('importance', ascending=False)

    print("🔝 Top 15 Most Important Features:")
    print(feature_importance_df.head(15).to_string(index=False))

    # Save feature importance
    feature_importance_df.to_csv('feature_importance.csv', index=False)
    print("✅ Feature importance saved to 'feature_importance.csv'")

# Strategy-Specific Analysis
print("\n📈 Strategy-Specific Performance Analysis...")
print("-" * 50)

# Analyze performance by strategy
strategy_performance = {}
for strategy in STRATEGIES:
    strategy_mask = actionable_data['strategy_source'] == strategy
    strategy_indices = actionable_data[strategy_mask].index

    # Get test indices for this strategy
    test_strategy_mask = X_test.index.isin(strategy_indices)
    if test_strategy_mask.sum() > 0:
        strategy_y_test = y_test[test_strategy_mask]
        strategy_y_pred = best_predictions[test_strategy_mask]

        strategy_accuracy = accuracy_score(strategy_y_test, strategy_y_pred)
        strategy_performance[strategy] = {
            'accuracy': strategy_accuracy,
            'samples': len(strategy_y_test),
            'predictions': strategy_y_pred,
            'actual': strategy_y_test
        }

        print(f"✅ {STRATEGY_NAMES[strategy]} ({strategy}): "
              f"Accuracy = {strategy_accuracy:.4f} "
              f"(Samples: {len(strategy_y_test)})")

# Model Persistence
print("\n💾 Saving Models and Components...")
print("-" * 40)

# Create models directory
models_dir = 'trained_models'
os.makedirs(models_dir, exist_ok=True)

# Save all trained models
for name, model in trained_models.items():
    model_path = os.path.join(models_dir, f'{name.lower()}_model.pkl')
    joblib.dump(model, model_path)
    print(f"✅ {name} model saved to {model_path}")

# Save preprocessing components
scaler_path = os.path.join(models_dir, 'feature_scaler.pkl')
joblib.dump(scaler, scaler_path)
print(f"✅ Feature scaler saved to {scaler_path}")

label_encoder_path = os.path.join(models_dir, 'label_encoder.pkl')
joblib.dump(label_encoder, label_encoder_path)
print(f"✅ Label encoder saved to {label_encoder_path}")

# Save feature columns
feature_columns_path = os.path.join(models_dir, 'feature_columns.pkl')
joblib.dump(feature_columns, feature_columns_path)
print(f"✅ Feature columns saved to {feature_columns_path}")

# Save model performance summary
performance_summary = {
    'model_scores': model_scores,
    'strategy_performance': strategy_performance,
    'best_model': best_model_name,
    'feature_count': len(feature_columns),
    'training_samples': len(X_train),
    'test_samples': len(X_test),
    'strategies_used': STRATEGIES,
    'timestamp': datetime.now().isoformat()
}

summary_path = os.path.join(models_dir, 'training_summary.pkl')
joblib.dump(performance_summary, summary_path)
print(f"✅ Training summary saved to {summary_path}")

# Final Summary
print("\n🎉 Training Complete - Summary")
print("=" * 50)
print(f"🏆 Best Model: {best_model_name}")
print(f"📊 Best Accuracy: {best_accuracy:.4f}")
print(f"🎯 Features Used: {len(feature_columns)}")
print(f"📈 Training Samples: {len(X_train)}")
print(f"🧪 Test Samples: {len(X_test)}")
print(f"🔧 Strategies: {', '.join(STRATEGIES)}")

print("\n📁 Saved Files:")
print(f"  • Models: {models_dir}/")
print(f"  • Feature Importance: feature_importance.csv")
print(f"  • Training Summary: {summary_path}")

print("\n✨ Your trading bot is now trained and ready!")
print("🚀 Use the ensemble model for best performance.")
print("📊 Check feature_importance.csv for insights.")
print("=" * 50)
