import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.preprocessing import StandardScaler, LabelEncoder
import joblib
import os

print("🚀 Advanced Trading Bot Training System")
print("Training on Strategies 1, 3, and 4")
print("=" * 60)

# Load and process data
print("\n📊 Loading Strategy Data...")
strategies = {}

# Strategy 1
s1 = pd.read_csv('output_signals_S1.csv')
s1['strategy1_signal'] = s1['strategy1_signal'].fillna(0)
s1['strategy'] = 'S1'
s1['label'] = s1['strategy1_signal'].apply(lambda x: 'BUY' if x == 1 else ('SELL' if x == -1 else 'HOLD'))
strategies['S1'] = s1

# Strategy 3
s3 = pd.read_csv('output_signals_S3.csv')
s3['strategy3_signal'] = s3['strategy3_signal'].fillna(0)
s3['strategy'] = 'S3'
s3['label'] = s3['strategy3_signal'].apply(lambda x: 'BUY' if x == 1 else ('SELL' if x == -1 else 'HOLD'))
strategies['S3'] = s3

# Strategy 4
s4 = pd.read_csv('output_signals_S4.csv')
s4['strategy4_signal'] = s4['strategy4_signal'].fillna(0)
s4['strategy'] = 'S4'
s4['label'] = s4['strategy4_signal'].apply(lambda x: 'BUY' if x == 1 else ('SELL' if x == -1 else 'HOLD'))
strategies['S4'] = s4

# Print signal counts
for name, df in strategies.items():
    counts = df['label'].value_counts()
    print(f"✅ {name}: BUY={counts.get('BUY', 0)}, SELL={counts.get('SELL', 0)}, HOLD={counts.get('HOLD', 0)}")

# Filter actionable signals
actionable_data = []
for name, df in strategies.items():
    actionable = df[df['label'].isin(['BUY', 'SELL'])].copy()
    print(f"   Actionable {name}: {len(actionable)} signals")
    actionable_data.append(actionable)

# Combine all actionable data
all_data = pd.concat(actionable_data, ignore_index=True)
print(f"\n✅ Total actionable signals: {len(all_data)}")

# Advanced feature engineering
print("\n⚙️ Advanced Feature Engineering...")

# Basic price features
all_data['body_size'] = abs(all_data['close'] - all_data['open'])
all_data['upper_wick'] = all_data['high'] - all_data[['open', 'close']].max(axis=1)
all_data['lower_wick'] = all_data[['open', 'close']].min(axis=1) - all_data['low']
all_data['total_range'] = all_data['high'] - all_data['low']

# Ratios and percentages
all_data['body_to_range'] = all_data['body_size'] / (all_data['total_range'] + 1e-8)
all_data['upper_wick_ratio'] = all_data['upper_wick'] / (all_data['body_size'] + 1e-8)
all_data['lower_wick_ratio'] = all_data['lower_wick'] / (all_data['body_size'] + 1e-8)

# Volume features
all_data['volume_price_trend'] = all_data['volume'] * (all_data['close'] - all_data['open'])
all_data['volume_normalized'] = all_data['volume'] / (all_data['volume_sma'] + 1e-8)

# Technical indicator features
all_data['rsi_oversold'] = (all_data['rsi'] < 30).astype(int)
all_data['rsi_overbought'] = (all_data['rsi'] > 70).astype(int)
all_data['rsi_neutral'] = ((all_data['rsi'] >= 30) & (all_data['rsi'] <= 70)).astype(int)

# Bollinger Bands features
all_data['bb_squeeze'] = (all_data['bb_width'] < all_data['bb_width'].rolling(20).mean()).astype(int)
all_data['price_above_bb_upper'] = (all_data['close'] > all_data['bb_upper']).astype(int)
all_data['price_below_bb_lower'] = (all_data['close'] < all_data['bb_lower']).astype(int)

# MACD features
all_data['macd_bullish'] = (all_data['macd'] > all_data['macd_signal']).astype(int)
all_data['macd_bearish'] = (all_data['macd'] < all_data['macd_signal']).astype(int)

# EMA features
all_data['price_above_ema20'] = (all_data['close'] > all_data['ema_20']).astype(int)
all_data['price_above_ema50'] = (all_data['close'] > all_data['ema_50']).astype(int)
all_data['ema20_above_ema50'] = (all_data['ema_20'] > all_data['ema_50']).astype(int)

# Candle patterns
all_data['is_bullish'] = (all_data['close'] > all_data['open']).astype(int)
all_data['is_bearish'] = (all_data['close'] < all_data['open']).astype(int)
all_data['is_doji'] = (abs(all_data['close'] - all_data['open']) < 0.0001).astype(int)

# Momentum features
all_data['price_momentum'] = all_data['close'].pct_change(3).fillna(0)
all_data['volume_momentum'] = all_data['volume'].pct_change(3).fillna(0)

print("✅ Advanced features created")

# Clean data
all_data = all_data.replace([np.inf, -np.inf], np.nan)
all_data = all_data.fillna(all_data.select_dtypes(include=[np.number]).mean())

# Select features
exclude_cols = [
    'time', 'direction', 'label', 'strategy',
    'strategy1_signal', 'strategy3_signal', 'strategy4_signal'
]

feature_cols = [col for col in all_data.columns 
                if col not in exclude_cols and all_data[col].dtype in ['int64', 'float64']]

print(f"✅ Selected {len(feature_cols)} features for training")

# Prepare data
X = all_data[feature_cols].values
y = all_data['label'].values

# Encode labels
le = LabelEncoder()
y_encoded = le.fit_transform(y)
print(f"✅ Labels encoded: {dict(zip(le.classes_, range(len(le.classes_))))}")

# Scale features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Train/test split
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

print(f"\n📊 Dataset: Training={len(X_train)}, Testing={len(X_test)}")

# Train multiple models
print(f"\n🤖 Training Advanced Models...")

models = {}

# RandomForest
print("🌲 Training RandomForest...")
rf = RandomForestClassifier(
    n_estimators=200, max_depth=15, min_samples_split=5,
    random_state=42, n_jobs=-1
)
rf.fit(X_train, y_train)
rf_pred = rf.predict(X_test)
rf_acc = accuracy_score(y_test, rf_pred)
rf_cv = cross_val_score(rf, X_train, y_train, cv=5).mean()
models['RandomForest'] = (rf, rf_acc, rf_cv, rf_pred)
print(f"   ✅ Accuracy: {rf_acc:.4f}, CV: {rf_cv:.4f}")

# GradientBoosting
print("🚀 Training GradientBoosting...")
gb = GradientBoostingClassifier(
    n_estimators=150, learning_rate=0.1, max_depth=8,
    random_state=42
)
gb.fit(X_train, y_train)
gb_pred = gb.predict(X_test)
gb_acc = accuracy_score(y_test, gb_pred)
gb_cv = cross_val_score(gb, X_train, y_train, cv=5).mean()
models['GradientBoosting'] = (gb, gb_acc, gb_cv, gb_pred)
print(f"   ✅ Accuracy: {gb_acc:.4f}, CV: {gb_cv:.4f}")

# LogisticRegression
print("📈 Training LogisticRegression...")
lr = LogisticRegression(random_state=42, max_iter=1000, C=1.0)
lr.fit(X_train, y_train)
lr_pred = lr.predict(X_test)
lr_acc = accuracy_score(y_test, lr_pred)
lr_cv = cross_val_score(lr, X_train, y_train, cv=5).mean()
models['LogisticRegression'] = (lr, lr_acc, lr_cv, lr_pred)
print(f"   ✅ Accuracy: {lr_acc:.4f}, CV: {lr_cv:.4f}")

# Ensemble
print("🎭 Creating Ensemble...")
ensemble = VotingClassifier(
    estimators=[('rf', rf), ('gb', gb), ('lr', lr)],
    voting='soft'
)
ensemble.fit(X_train, y_train)
ensemble_pred = ensemble.predict(X_test)
ensemble_acc = accuracy_score(y_test, ensemble_pred)
ensemble_cv = cross_val_score(ensemble, X_train, y_train, cv=5).mean()
models['Ensemble'] = (ensemble, ensemble_acc, ensemble_cv, ensemble_pred)
print(f"   ✅ Accuracy: {ensemble_acc:.4f}, CV: {ensemble_cv:.4f}")

# Find best model
best_name = max(models.keys(), key=lambda x: models[x][1])
best_model, best_acc, best_cv, best_pred = models[best_name]

print(f"\n🏆 Best Model: {best_name}")
print(f"📊 Best Accuracy: {best_acc:.4f}")
print(f"🎯 Best CV Score: {best_cv:.4f}")

# Detailed evaluation
print(f"\n📋 Classification Report ({best_name}):")
print(classification_report(y_test, best_pred, target_names=le.classes_))

print(f"\n🔍 Confusion Matrix:")
cm = confusion_matrix(y_test, best_pred)
print(cm)

# Save all models
print(f"\n💾 Saving Models...")
os.makedirs('trained_models', exist_ok=True)

for name, (model, acc, cv, pred) in models.items():
    filename = f'trained_models/{name.lower()}_model.pkl'
    joblib.dump(model, filename)
    print(f"✅ Saved {name} → {filename}")

# Save preprocessing components
joblib.dump(scaler, 'trained_models/advanced_scaler.pkl')
joblib.dump(le, 'trained_models/advanced_label_encoder.pkl')
joblib.dump(feature_cols, 'trained_models/advanced_features.pkl')

# Save summary
summary = {
    'best_model': best_name,
    'best_accuracy': best_acc,
    'best_cv_score': best_cv,
    'model_results': {name: {'accuracy': acc, 'cv_score': cv} for name, (_, acc, cv, _) in models.items()},
    'feature_count': len(feature_cols),
    'training_samples': len(X_train),
    'test_samples': len(X_test),
    'strategies': ['S1', 'S3', 'S4']
}

joblib.dump(summary, 'trained_models/advanced_summary.pkl')

print(f"✅ Saved preprocessing components and summary")

# Feature importance
if hasattr(best_model, 'feature_importances_'):
    print(f"\n🎯 Top 10 Most Important Features ({best_name}):")
    importance_pairs = list(zip(feature_cols, best_model.feature_importances_))
    importance_pairs.sort(key=lambda x: x[1], reverse=True)
    for i, (feature, importance) in enumerate(importance_pairs[:10]):
        print(f"{i+1:2d}. {feature}: {importance:.4f}")

# Final summary
print(f"\n🎉 ADVANCED TRAINING COMPLETED!")
print("=" * 60)
print(f"🏆 Best Model: {best_name}")
print(f"📊 Accuracy: {best_acc:.4f}")
print(f"🎯 CV Score: {best_cv:.4f}")
print(f"📈 Features: {len(feature_cols)}")
print(f"🔧 Strategies: S1, S3, S4")
print(f"📁 Models: trained_models/")
print(f"\n✨ Your advanced trading bot is ready!")
print("🚀 Use the ensemble model for optimal performance.")
print("=" * 60)
