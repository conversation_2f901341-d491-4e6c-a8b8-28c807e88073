#!/usr/bin/env python3
"""
Simple Complete Trading Bot Training for Strategies 1, 3, and 4
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
import joblib
import os
from datetime import datetime

print("🚀 Simple Complete Trading Bot Training")
print("Training on Strategies 1, 3, and 4")
print("=" * 60)

# Load strategy data
strategies = {
    'S1': 'output_signals_S1.csv',
    'S3': 'output_signals_S3.csv', 
    'S4': 'output_signals_S4.csv'
}

print("\n📊 Loading and processing data...")
all_data = []

for strategy, filename in strategies.items():
    print(f"Loading {filename}...")
    df = pd.read_csv(filename)
    
    # Add strategy identifier
    df['strategy_source'] = strategy
    
    # Fill NaN values in strategy signal column
    signal_col = f'strategy{strategy[1:]}_signal'
    df[signal_col] = df[signal_col].fillna(0)
    
    # Create labels
    def create_label(signal):
        if signal == 1:
            return 'BUY'
        elif signal == -1:
            return 'SELL'
        else:
            return 'HOLD'
    
    df['trading_signal'] = df[signal_col].apply(create_label)
    
    # Count signals
    signal_counts = df['trading_signal'].value_counts()
    print(f"✅ {strategy}: BUY={signal_counts.get('BUY', 0)}, "
          f"SELL={signal_counts.get('SELL', 0)}, HOLD={signal_counts.get('HOLD', 0)}")
    
    all_data.append(df)

# Combine all data
data = pd.concat(all_data, ignore_index=True)
print(f"\n✅ Combined dataset: {len(data)} rows")

# Filter for actionable signals
actionable = data[data['trading_signal'].isin(['BUY', 'SELL'])].copy()
print(f"✅ Actionable signals: {len(actionable)} rows")

# Basic feature engineering
print("\n⚙️ Feature engineering...")
actionable['body_size'] = abs(actionable['close'] - actionable['open'])
actionable['upper_wick'] = actionable['high'] - actionable[['open', 'close']].max(axis=1)
actionable['lower_wick'] = actionable[['open', 'close']].min(axis=1) - actionable['low']
actionable['total_range'] = actionable['high'] - actionable['low']
actionable['body_to_range'] = actionable['body_size'] / (actionable['total_range'] + 1e-8)
actionable['volume_price_trend'] = actionable['volume'] * (actionable['close'] - actionable['open'])
actionable['is_bullish'] = (actionable['close'] > actionable['open']).astype(int)
actionable['is_bearish'] = (actionable['close'] < actionable['open']).astype(int)

# Clean data
actionable = actionable.replace([np.inf, -np.inf], np.nan)
actionable = actionable.fillna(actionable.select_dtypes(include=[np.number]).mean())

# Select features
exclude_cols = [
    'time', 'direction', 'trading_signal', 'strategy_source',
    'strategy1_signal', 'strategy3_signal', 'strategy4_signal'
]

feature_cols = [col for col in actionable.columns 
                if col not in exclude_cols and actionable[col].dtype in ['int64', 'float64']]

print(f"✅ Selected {len(feature_cols)} features")

# Prepare data
X = actionable[feature_cols].values
y = actionable['trading_signal'].values

# Encode labels
le = LabelEncoder()
y_encoded = le.fit_transform(y)
print(f"✅ Labels: {dict(zip(le.classes_, range(len(le.classes_))))}")

# Scale features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Train/test split
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

print(f"\n📊 Training: {len(X_train)} samples, Testing: {len(X_test)} samples")

# Train models
print(f"\n🤖 Training models...")

# RandomForest
print("Training RandomForest...")
rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
rf.fit(X_train, y_train)
rf_pred = rf.predict(X_test)
rf_acc = accuracy_score(y_test, rf_pred)
rf_cv = cross_val_score(rf, X_train, y_train, cv=5).mean()
print(f"✅ RandomForest: {rf_acc:.4f} (CV: {rf_cv:.4f})")

# GradientBoosting
print("Training GradientBoosting...")
gb = GradientBoostingClassifier(n_estimators=100, random_state=42)
gb.fit(X_train, y_train)
gb_pred = gb.predict(X_test)
gb_acc = accuracy_score(y_test, gb_pred)
gb_cv = cross_val_score(gb, X_train, y_train, cv=5).mean()
print(f"✅ GradientBoosting: {gb_acc:.4f} (CV: {gb_cv:.4f})")

# LogisticRegression
print("Training LogisticRegression...")
lr = LogisticRegression(random_state=42, max_iter=1000)
lr.fit(X_train, y_train)
lr_pred = lr.predict(X_test)
lr_acc = accuracy_score(y_test, lr_pred)
lr_cv = cross_val_score(lr, X_train, y_train, cv=5).mean()
print(f"✅ LogisticRegression: {lr_acc:.4f} (CV: {lr_cv:.4f})")

# Ensemble
print("Creating Ensemble...")
ensemble = VotingClassifier(
    estimators=[('rf', rf), ('gb', gb), ('lr', lr)],
    voting='soft'
)
ensemble.fit(X_train, y_train)
ensemble_pred = ensemble.predict(X_test)
ensemble_acc = accuracy_score(y_test, ensemble_pred)
ensemble_cv = cross_val_score(ensemble, X_train, y_train, cv=5).mean()
print(f"✅ Ensemble: {ensemble_acc:.4f} (CV: {ensemble_cv:.4f})")

# Find best model
models = {
    'RandomForest': (rf, rf_acc, rf_cv),
    'GradientBoosting': (gb, gb_acc, gb_cv),
    'LogisticRegression': (lr, lr_acc, lr_cv),
    'Ensemble': (ensemble, ensemble_acc, ensemble_cv)
}

best_name = max(models.keys(), key=lambda x: models[x][1])
best_model, best_acc, best_cv = models[best_name]

print(f"\n🏆 Best Model: {best_name} (Accuracy: {best_acc:.4f})")

# Detailed evaluation
print(f"\n📋 Classification Report ({best_name}):")
best_pred = best_model.predict(X_test)
print(classification_report(y_test, best_pred, target_names=le.classes_))

print(f"\n🔍 Confusion Matrix:")
cm = confusion_matrix(y_test, best_pred)
print(cm)

# Save models
print(f"\n💾 Saving models...")
os.makedirs('trained_models', exist_ok=True)

# Save all models
joblib.dump(rf, 'trained_models/randomforest_model.pkl')
joblib.dump(gb, 'trained_models/gradientboosting_model.pkl')
joblib.dump(lr, 'trained_models/logisticregression_model.pkl')
joblib.dump(ensemble, 'trained_models/ensemble_model.pkl')

# Save preprocessing components
joblib.dump(scaler, 'trained_models/scaler.pkl')
joblib.dump(le, 'trained_models/label_encoder.pkl')
joblib.dump(feature_cols, 'trained_models/feature_columns.pkl')

# Save summary
summary = {
    'best_model': best_name,
    'best_accuracy': best_acc,
    'best_cv_score': best_cv,
    'model_accuracies': {name: acc for name, (_, acc, _) in models.items()},
    'feature_count': len(feature_cols),
    'training_samples': len(X_train),
    'test_samples': len(X_test),
    'strategies': list(strategies.keys()),
    'timestamp': datetime.now().isoformat()
}

joblib.dump(summary, 'trained_models/training_summary.pkl')

print(f"✅ All models saved to trained_models/")

# Final summary
print(f"\n🎉 TRAINING COMPLETED SUCCESSFULLY!")
print("=" * 60)
print(f"🏆 Best Model: {best_name}")
print(f"📊 Best Accuracy: {best_acc:.4f}")
print(f"🎯 CV Score: {best_cv:.4f}")
print(f"📈 Features: {len(feature_cols)}")
print(f"🔧 Strategies: S1, S3, S4")
print(f"📁 Models saved in: trained_models/")
print(f"\n✨ Your trading bot is ready!")
print("🚀 Use the ensemble model for best performance.")
print("=" * 60)

# Show feature importance for RandomForest
if hasattr(rf, 'feature_importances_'):
    print(f"\n🎯 Top 10 Most Important Features (RandomForest):")
    feature_importance = list(zip(feature_cols, rf.feature_importances_))
    feature_importance.sort(key=lambda x: x[1], reverse=True)
    for i, (feature, importance) in enumerate(feature_importance[:10]):
        print(f"{i+1:2d}. {feature}: {importance:.4f}")

print(f"\n🎊 Training complete! Your bot is ready to trade!")
