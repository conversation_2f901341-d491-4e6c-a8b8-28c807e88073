import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
import joblib
import os

print("🚀 Quick Trading Bot Training")
print("=" * 40)

# Load Strategy 1 data
print("Loading Strategy 1...")
s1 = pd.read_csv('output_signals_S1.csv')
s1['strategy1_signal'] = s1['strategy1_signal'].fillna(0)
s1['strategy'] = 'S1'

# Load Strategy 3 data  
print("Loading Strategy 3...")
s3 = pd.read_csv('output_signals_S3.csv')
s3['strategy3_signal'] = s3['strategy3_signal'].fillna(0)
s3['strategy'] = 'S3'

# Load Strategy 4 data
print("Loading Strategy 4...")
s4 = pd.read_csv('output_signals_S4.csv')
s4['strategy4_signal'] = s4['strategy4_signal'].fillna(0)
s4['strategy'] = 'S4'

# Create labels for each strategy
def create_s1_labels(signal):
    return 'BUY' if signal == 1 else ('SELL' if signal == -1 else 'HOLD')

def create_s3_labels(signal):
    return 'BUY' if signal == 1 else ('SELL' if signal == -1 else 'HOLD')

def create_s4_labels(signal):
    return 'BUY' if signal == 1 else ('SELL' if signal == -1 else 'HOLD')

s1['label'] = s1['strategy1_signal'].apply(create_s1_labels)
s3['label'] = s3['strategy3_signal'].apply(create_s3_labels)
s4['label'] = s4['strategy4_signal'].apply(create_s4_labels)

print(f"S1 signals: {s1['label'].value_counts().to_dict()}")
print(f"S3 signals: {s3['label'].value_counts().to_dict()}")
print(f"S4 signals: {s4['label'].value_counts().to_dict()}")

# Filter actionable signals
s1_actionable = s1[s1['label'].isin(['BUY', 'SELL'])]
s3_actionable = s3[s3['label'].isin(['BUY', 'SELL'])]
s4_actionable = s4[s4['label'].isin(['BUY', 'SELL'])]

print(f"Actionable signals: S1={len(s1_actionable)}, S3={len(s3_actionable)}, S4={len(s4_actionable)}")

# Combine actionable data
all_actionable = pd.concat([s1_actionable, s3_actionable, s4_actionable], ignore_index=True)
print(f"Total actionable signals: {len(all_actionable)}")

# Select basic features
feature_cols = ['open', 'high', 'low', 'close', 'volume', 'rsi', 'macd', 
                'bb_upper', 'bb_lower', 'bb_sma', 'ema_20', 'ema_50']

# Prepare data
X = all_actionable[feature_cols].fillna(0)
y = all_actionable['label']

print(f"Features: {X.shape[1]}, Samples: {X.shape[0]}")

# Encode labels
le = LabelEncoder()
y_encoded = le.fit_transform(y)
print(f"Label encoding: {dict(zip(le.classes_, range(len(le.classes_))))}")

# Scale features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Train/test split
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

print(f"Training: {len(X_train)}, Testing: {len(X_test)}")

# Train model
print("Training RandomForest...")
model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
model.fit(X_train, y_train)

# Evaluate
y_pred = model.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)

print(f"Accuracy: {accuracy:.4f}")
print("\nClassification Report:")
print(classification_report(y_test, y_pred, target_names=le.classes_))

# Save model
os.makedirs('trained_models', exist_ok=True)
joblib.dump(model, 'trained_models/quick_model.pkl')
joblib.dump(scaler, 'trained_models/quick_scaler.pkl')
joblib.dump(le, 'trained_models/quick_label_encoder.pkl')
joblib.dump(feature_cols, 'trained_models/quick_features.pkl')

print("\n✅ Model saved successfully!")
print("🎉 Training completed!")
print(f"📁 Files saved in: trained_models/")
print(f"🎯 Final accuracy: {accuracy:.4f}")
