#!/usr/bin/env python3
"""
Enhanced Trading Bot Training System
Trains models on Strategies 1, 3, and 4
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
import joblib
import os
from datetime import datetime

print("🚀 Enhanced Trading Bot Training System")
print("=" * 60)

# Configuration
STRATEGIES = ['S1', 'S3', 'S4']
STRATEGY_NAMES = {
    'S1': 'Breakout with Volume',
    'S3': 'Support/Resistance Rejection', 
    'S4': 'Trendline Break with Rejection'
}

# Load and combine strategy data
print("\n📊 Loading Strategy Data...")
print("-" * 40)

all_data = []
for strategy in STRATEGIES:
    file_path = f'output_signals_{strategy}.csv'
    print(f"Loading {file_path}...")
    
    df = pd.read_csv(file_path)
    df['strategy_source'] = strategy
    
    # Fill NaN values in strategy signal column
    signal_col = f'strategy{strategy[1:]}_signal'
    df[signal_col] = df[signal_col].fillna(0)
    
    print(f"✅ {strategy}: {df.shape[0]} rows, {df[signal_col].value_counts().to_dict()}")
    all_data.append(df)

# Combine all data
data = pd.concat(all_data, ignore_index=True)
print(f"\n✅ Combined data: {data.shape[0]} rows, {data.shape[1]} columns")

# Create strategy-based labels
print("\n🏷️ Creating Strategy Labels...")
def get_strategy_label(row):
    strategy = row['strategy_source']
    signal_col = f'strategy{strategy[1:]}_signal'
    signal = row[signal_col]
    
    if signal == 1:
        return 'BUY'
    elif signal == -1:
        return 'SELL'
    else:
        return 'HOLD'

data['strategy_label'] = data.apply(get_strategy_label, axis=1)
label_counts = data['strategy_label'].value_counts()
print(f"✅ Label distribution: {label_counts.to_dict()}")

# Filter for actionable signals only
actionable_data = data[data['strategy_label'].isin(['BUY', 'SELL'])].copy()
print(f"✅ Actionable signals: {len(actionable_data)} rows")

if len(actionable_data) < 100:
    print("❌ Not enough actionable signals for training!")
    exit(1)

# Feature Engineering
print("\n⚙️ Feature Engineering...")
def add_features(df):
    df = df.copy()
    
    # Price features
    df['body_size'] = abs(df['close'] - df['open'])
    df['upper_wick'] = df['high'] - df[['open', 'close']].max(axis=1)
    df['lower_wick'] = df[['open', 'close']].min(axis=1) - df['low']
    df['total_range'] = df['high'] - df['low']
    
    # Ratios
    df['body_to_range'] = df['body_size'] / (df['total_range'] + 1e-8)
    df['upper_wick_ratio'] = df['upper_wick'] / (df['body_size'] + 1e-8)
    df['lower_wick_ratio'] = df['lower_wick'] / (df['body_size'] + 1e-8)
    
    # Volume features
    df['volume_price_trend'] = df['volume'] * (df['close'] - df['open'])
    
    # Momentum
    df['price_momentum_3'] = df['close'].pct_change(3)
    df['price_momentum_5'] = df['close'].pct_change(5)
    
    # Candle types
    df['is_bullish'] = (df['close'] > df['open']).astype(int)
    df['is_bearish'] = (df['close'] < df['open']).astype(int)
    
    return df

actionable_data = add_features(actionable_data)
print("✅ Technical features added")

# Handle infinite/NaN values
actionable_data = actionable_data.replace([np.inf, -np.inf], np.nan)
actionable_data = actionable_data.fillna(actionable_data.mean())
print("✅ Cleaned infinite/NaN values")

# Prepare features
exclude_cols = [
    'time', 'direction', 'strategy_label', 'strategy_source',
    'strategy1_signal', 'strategy3_signal', 'strategy4_signal'
]

feature_cols = [col for col in actionable_data.columns if col not in exclude_cols]
print(f"✅ Selected {len(feature_cols)} features")

X = actionable_data[feature_cols]
y = actionable_data['strategy_label']

# Encode labels
label_encoder = LabelEncoder()
y_encoded = label_encoder.fit_transform(y)
print(f"✅ Label encoding: {dict(zip(label_encoder.classes_, range(len(label_encoder.classes_))))}")

# Scale features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
print("✅ Features scaled")

# Train/Test split
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

print(f"✅ Training: {X_train.shape[0]} samples")
print(f"✅ Testing: {X_test.shape[0]} samples")

# Model Training
print("\n🤖 Training Models...")
print("-" * 30)

models = {
    'RandomForest': RandomForestClassifier(
        n_estimators=200, max_depth=15, min_samples_split=5,
        random_state=42, n_jobs=-1
    ),
    'GradientBoosting': GradientBoostingClassifier(
        n_estimators=150, learning_rate=0.1, max_depth=8,
        random_state=42
    ),
    'LogisticRegression': LogisticRegression(
        random_state=42, max_iter=1000
    )
}

trained_models = {}
results = {}

for name, model in models.items():
    print(f"\n🔄 Training {name}...")
    
    # Train
    model.fit(X_train, y_train)
    
    # Evaluate
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_train, y_train, cv=5)
    
    print(f"✅ Accuracy: {accuracy:.4f}")
    print(f"✅ CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    trained_models[name] = model
    results[name] = {
        'accuracy': accuracy,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'predictions': y_pred
    }

# Ensemble Model
print("\n🎭 Creating Ensemble...")
ensemble = VotingClassifier(
    estimators=[(name, model) for name, model in trained_models.items()],
    voting='soft'
)
ensemble.fit(X_train, y_train)

ensemble_pred = ensemble.predict(X_test)
ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
ensemble_cv = cross_val_score(ensemble, X_train, y_train, cv=5)

print(f"✅ Ensemble Accuracy: {ensemble_accuracy:.4f}")
print(f"✅ Ensemble CV: {ensemble_cv.mean():.4f} ± {ensemble_cv.std():.4f}")

trained_models['Ensemble'] = ensemble
results['Ensemble'] = {
    'accuracy': ensemble_accuracy,
    'cv_mean': ensemble_cv.mean(),
    'cv_std': ensemble_cv.std(),
    'predictions': ensemble_pred
}

# Find best model
best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
best_model = trained_models[best_model_name]
best_accuracy = results[best_model_name]['accuracy']

print(f"\n🏆 Best Model: {best_model_name} (Accuracy: {best_accuracy:.4f})")

# Detailed evaluation
print(f"\n📋 Classification Report - {best_model_name}:")
print(classification_report(y_test, results[best_model_name]['predictions'],
                          target_names=label_encoder.classes_))

# Save models and components
print("\n💾 Saving Models...")
os.makedirs('trained_models', exist_ok=True)

# Save all models
for name, model in trained_models.items():
    joblib.dump(model, f'trained_models/{name.lower()}_model.pkl')
    print(f"✅ Saved {name} model")

# Save preprocessing components
joblib.dump(scaler, 'trained_models/scaler.pkl')
joblib.dump(label_encoder, 'trained_models/label_encoder.pkl')
joblib.dump(feature_cols, 'trained_models/feature_columns.pkl')

# Save training summary
summary = {
    'results': results,
    'best_model': best_model_name,
    'strategies': STRATEGIES,
    'feature_count': len(feature_cols),
    'training_samples': len(X_train),
    'test_samples': len(X_test),
    'timestamp': datetime.now().isoformat()
}

joblib.dump(summary, 'trained_models/training_summary.pkl')

print("\n🎉 Training Complete!")
print("=" * 50)
print(f"🏆 Best Model: {best_model_name}")
print(f"📊 Accuracy: {best_accuracy:.4f}")
print(f"🎯 Features: {len(feature_cols)}")
print(f"📈 Training Samples: {len(X_train)}")
print(f"🧪 Test Samples: {len(X_test)}")
print(f"🔧 Strategies: {', '.join(STRATEGIES)}")
print("\n✨ Your trading bot is ready!")
